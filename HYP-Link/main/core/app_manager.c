/* Standard includes */
#include <stdio.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>

/* FreeRTOS includes */
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/timers.h"

/* ESP-IDF System includes */
#include "esp_system.h"
#include "esp_check.h"
#include "esp_err.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "esp_flash.h"
#include "nvs_flash.h"
#include "sdkconfig.h"

/* ESP-IDF Networking includes */
#include "esp_netif.h"
#include "esp_netif_sntp.h"
#include "esp_sntp.h"
#include "lwip/ip_addr.h"
#include "esp_event.h"
#include "mdns.h"

/* OpenThread includes */
#include "esp_openthread.h"
#include "esp_openthread_types.h"
#include "esp_openthread_border_router.h"
#include "esp_ot_config.h"

/* Driver includes */
#include "driver/gpio.h"
#include "driver/ledc.h"
#include "driver/uart.h"
#include "driver/usb_serial_jtag.h"

/* ESP Secure Certificate Manager */
#include "esp_secure_cert_read.h"

/* Networking components */
#include "networking/sntp/sntp_client.h"
#include "network_transport.h"
#include "mqtt_manager.h"

/* Application components */
#include "app_wifi.h"
#include "app_manager.h"
#include "border_router_launch.h"
#include "coap_server.h"
#include "reporting_task.h"
#include "ota_task.h"
#include "ota_pal.h"
#include "pppos_client.h"
#include "log_upload.h"
#include "map.h"

/* Protocol examples */
#include "protocol_examples_common.h"

/* ESP SPIFFS include */
#include "esp_spiffs.h"

/* ESP VFS eventfd */
#include "esp_vfs_eventfd.h"

/* NVS includes */
#include "nvs.h"
#include "esp_nimble_hci.h"
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "host/util/util.h"
#include "services/gap/ble_svc_gap.h"
#include "services/gatt/ble_svc_gatt.h"
#include "networking/ble/ble_spp_server.h"

//---------------------------------------------------------------------------------------------

#define TAG "APP_MANAGER"
#define NVS_NAMESPACE "settings"      // NVS namespace for configuration
#define CONFIG_PRESENT_KEY "configured" // NVS key for configuration flag

/* Button Configuration */
#define BUTTON_GPIO          46        // Button GPIO pin
#define LONG_PRESS_TIME_MS   10000     // Long press threshold (10 seconds)
#define SHORT_PRESS_TIME_MS  3000      // Short press threshold (3 seconds)

/* Buffer and Stack Size Config */
#define BUF_SIZE             1024      // Buffer size for data
#define ECHO_TASK_STACK_SIZE 4096      // Stack size for the echo task

/* LEDC Timer Configuration */
#define LEDC_TIMER           LEDC_TIMER_0         // Use LEDC timer 0
#define LEDC_MODE            LEDC_LOW_SPEED_MODE  // Low-speed mode for LEDC

/* LED Output Pin Config */
#define BLUE_OUTPUT_IO       1          // GPIO pin for blue LED
#define GREEN_OUTPUT_IO      2          // GPIO pin for green LED
#define RED_OUTPUT_IO        3          // GPIO pin for red LED

/* LEDC Channel Config */
#define BLUE_CHANNEL         LEDC_CHANNEL_0       // Channel 0 for blue LED
#define GREEN_CHANNEL        LEDC_CHANNEL_2       // Channel 2 for green LED
#define RED_CHANNEL          LEDC_CHANNEL_3       // Channel 3 for red LED

/* LEDC Duty Cycle and Frequency */
#define LEDC_DUTY_RES        LEDC_TIMER_13_BIT    // 13-bit duty resolution
#define LEDC_DUTY            8192                 // 100% duty cycle for 13-bit resolution
#define LEDC_FREQUENCY       4000                 // Frequency set to 4 kHz

//---------------------------------------------------------------------------------------------

/* Task Handles for LED Control */
TaskHandle_t BlueHandle = NULL;    // Handle for blue LED task
TaskHandle_t GreenHandle = NULL;   // Handle for green LED task
TaskHandle_t RedHandle = NULL;     // Handle for red LED task

/* Amazon Root CA Certificate */
extern const char root_cert_auth_start[] asm("_binary_AmazonRootCA1_pem_start");
extern const char root_cert_auth_end[] asm("_binary_AmazonRootCA1_pem_end");

/* AWS Code Signing Certificate */
extern const char pcAwsCodeSigningCertPem[] asm("_binary_aws_codesign_crt_start");

/* Network Context */
static NetworkContext_t xNetworkContext;

//---------------------------------------------------------------------------------------------

/**
 * @brief Initializes the LEDC timer and channels for controlling RGB LEDs.
 *
 * This function configures the LEDC timer and three channels for blue, green, and red LEDs.
 * Each channel is associated with a specific GPIO pin and uses the same timer configuration.
 */
static void ledc_init(void);

/**
 * @brief Initialize the certificates, NVS, and network context.
 */
static void cert_init(void);

/**
 * @brief Initialize the button GPIO as input with pull-up enabled and create the button task.
 */
static void button_init(void);

/**
 * @brief Initialize the OpenThread Border Router (BR).
 */
static void ot_br_init(void);

/**
 * @brief Initialize the network context with MQTT endpoint, certificates, and TLS settings.
 *
 * @return pdPASS on success, pdFAIL on failure.
 */
static BaseType_t prvInitializeNetworkContext(void);

/**
 * @brief Start the reporting process and initialize the coreMQTT-Agent network manager.
 */
static void prvStartReporting(void);

/**
 * @brief Checks if the device configuration is present in NVS.
 *
 * @return true if the configuration flag is set to 1, false otherwise or on NVS error.
 */
static bool is_configured(void)
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open(NVS_NAMESPACE, NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        if (err == ESP_ERR_NVS_NOT_INITIALIZED) {
            ESP_LOGW(TAG, "NVS partition not initialized yet.");
        } else if (err == ESP_ERR_NVS_NOT_FOUND) {
             ESP_LOGI(TAG, "NVS namespace '%s' not found.", NVS_NAMESPACE);
        } else {
             ESP_LOGE(TAG, "Error (%s) opening NVS handle!", esp_err_to_name(err));
        }
        return false; // Treat NVS error or missing namespace as not configured
    }

    uint8_t configured_flag = 0;
    err = nvs_get_u8(nvs_handle, CONFIG_PRESENT_KEY, &configured_flag);
    nvs_close(nvs_handle);

    if (err == ESP_OK && configured_flag == 1) {
        ESP_LOGI(TAG, "Device is configured (flag=1).");
        return true;
    } else if (err == ESP_ERR_NVS_NOT_FOUND) {
        ESP_LOGI(TAG, "Configuration flag '%s' not found in NVS.", CONFIG_PRESENT_KEY);
        return false;
    } else if (err != ESP_OK) {
        ESP_LOGE(TAG, "Error (%s) reading NVS configured flag '%s'!", esp_err_to_name(err), CONFIG_PRESENT_KEY);
        return false; // Treat NVS read error as not configured
    }

    ESP_LOGI(TAG, "Configured flag is 0.");
    return false;
}

/**
 * @brief Checks if the 4G configuration is enabled.
 *
 * @return true if 4G is configured and enabled, false otherwise.
 */
static bool is_4g_configured(void)
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open(NVS_NAMESPACE, NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return false;
    }

    uint8_t cell_en = 0;
    err = nvs_get_u8(nvs_handle, "cell_en", &cell_en);
    
    nvs_close(nvs_handle);
    return (err == ESP_OK && cell_en == 1);
}

/**
 * @brief Checks if the WiFi configuration is enabled.
 *
 * @return true if WiFi is configured, false otherwise.
 */
static bool is_wifi_configured(void)
{
    return !is_4g_configured();
}

/**
 * @brief Checks if the Ethernet configuration is enabled.
 *
 * @return true if Ethernet is configured and enabled, false otherwise.
 */
static bool is_eth_configured(void)
{
    return !is_4g_configured();
}

/**
 * @brief Initialize application modules.
 */
void app_manager_init(void)
{
    ESP_LOGI(TAG, "Initializing application modules");

    // Initialize LED controller first for visual feedback
    ledc_init();

    // --- Initialize NVS ---
    // This is critical for checking config and for BLE bonding store
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND)
    {
        ESP_LOGW(TAG, "Erasing NVS partition...");
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // --- Check Configuration Status ---
    if (!is_configured())
    {
        ESP_LOGW(TAG, "Device not configured. Starting BLE SPP configuration mode...");
        ledtask_operation(BLUE, FAST); // Blue fast blink indicates BLE config mode

        nimble_port_init();
        ble_store_config_init();
        ESP_ERROR_CHECK(ble_spp_server_init());

        // Start the BLE Host task (advertisement, event handling)
        ble_spp_server_start();
        // Do not proceed with further initialization
        ESP_LOGI(TAG, "Halting normal boot process. Waiting for BLE configuration.");
        return;
    }

    // --- Device is Configured - Proceed with Normal Initialization ---
    ESP_LOGI(TAG, "Device configured. Starting normal operation...");
    ledtask_operation(GREEN, FAST); // Green fast blink indicates normal boot

    // Initialize certificates and network context (requires NVS)
    cert_init(); 

    // Initialize button inputs (for 10s erase)
    button_init();

    // Initialize and start Wi-Fi only if WiFi is configured
    if(is_wifi_configured() || is_eth_configured())
    {
        ESP_LOGI(TAG, "WiFi configuration found, initializing WiFi");
        app_wifi_init();
        app_wifi_start(POP_TYPE_MAC);
    }
    
    if(pppIsConnected)
    {
        ppp_isConnected();
    }

    // Initialize the OpenThread Border Router
    ot_br_init(); 

    // Initialize log capture
    init_log_capture();

    // Initialize map queue for sensor updates
    map_init_queue();

    ESP_LOGI(TAG, "Application initialization complete.");
}

//---------------------------------------------------------------------------------------------

static void ledc_init(void)
{
    // Prepare and then apply the LEDC PWM timer configuration
    ledc_timer_config_t ledc_timer = {
        .speed_mode = LEDC_MODE,
        .timer_num = LEDC_TIMER,
        .duty_resolution = LEDC_DUTY_RES,
        .freq_hz = LEDC_FREQUENCY, // Set output frequency at 4 kHz
        .clk_cfg = LEDC_AUTO_CLK};
    ESP_ERROR_CHECK(ledc_timer_config(&ledc_timer));

    // Prepare and then apply the LEDC PWM channel configuration
    ledc_channel_config_t ledc_channel = {
        .speed_mode = LEDC_MODE,
        .channel = BLUE_CHANNEL,
        .timer_sel = LEDC_TIMER,
        .intr_type = LEDC_INTR_DISABLE,
        .gpio_num = BLUE_OUTPUT_IO,
        .duty = LEDC_DUTY,
        .hpoint = 0};
    ESP_ERROR_CHECK(ledc_channel_config(&ledc_channel));
    ledc_channel_config_t ledc_channel2 = {
        .speed_mode = LEDC_MODE,
        .channel = GREEN_CHANNEL,
        .timer_sel = LEDC_TIMER,
        .intr_type = LEDC_INTR_DISABLE,
        .gpio_num = GREEN_OUTPUT_IO,
        .duty = LEDC_DUTY,
        .hpoint = 0};
    ESP_ERROR_CHECK(ledc_channel_config(&ledc_channel2));
    ledc_channel_config_t ledc_channel3 = {
        .speed_mode = LEDC_MODE,
        .channel = RED_CHANNEL,
        .timer_sel = LEDC_TIMER,
        .intr_type = LEDC_INTR_DISABLE,
        .gpio_num = RED_OUTPUT_IO,
        .duty = LEDC_DUTY,
        .hpoint = 0};
    ESP_ERROR_CHECK(ledc_channel_config(&ledc_channel3));

    ledtask_operation(GREEN, FAST);
}

/**
 * @brief Task to blink the blue LED at a fast rate (250ms ON, 250ms OFF).
 *
 * This task sets the duty cycle for the blue LED to create a fast blinking effect.
 *
 * @param arg Pointer to task parameters (not used).
 */
static void blue_fast_task(void *arg)
{
    while (1)
    {
        ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, BLUE_CHANNEL, LEDC_DUTY));
        ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, BLUE_CHANNEL));
        vTaskDelay(250 / portTICK_PERIOD_MS);

        ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, BLUE_CHANNEL, 0));
        ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, BLUE_CHANNEL));
        vTaskDelay(250 / portTICK_PERIOD_MS);
    }
}

/**
 * @brief Task to blink the green LED at a fast rate (250ms ON, 250ms OFF).
 *
 * This task sets the duty cycle for the green LED to create a fast blinking effect.
 *
 * @param arg Pointer to task parameters (not used).
 */
static void green_fast_task(void *arg)
{
    while (1)
    {
        ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, GREEN_CHANNEL, LEDC_DUTY));
        ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, GREEN_CHANNEL));
        vTaskDelay(250 / portTICK_PERIOD_MS);

        ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, GREEN_CHANNEL, 0));
        ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, GREEN_CHANNEL));
        vTaskDelay(250 / portTICK_PERIOD_MS);
    }
}

/**
 * @brief Task to blink the red LED at a fast rate (250ms ON, 250ms OFF).
 *
 * This task sets the duty cycle for the red LED to create a fast blinking effect.
 *
 * @param arg Pointer to task parameters (not used).
 */
static void red_fast_task(void *arg)
{
    while (1)
    {
        ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, RED_CHANNEL, LEDC_DUTY));
        ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, RED_CHANNEL));
        vTaskDelay(250 / portTICK_PERIOD_MS);

        ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, RED_CHANNEL, 0));
        ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, RED_CHANNEL));
        vTaskDelay(250 / portTICK_PERIOD_MS);
    }
}

/**
 * @brief Task to blink the blue LED at a slow rate (1s ON, 1s OFF).
 *
 * This task sets the duty cycle for the blue LED to create a slow blinking effect.
 *
 * @param arg Pointer to task parameters (not used).
 */
static void blue_slow_task(void *arg)
{
    while (1)
    {
        ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, BLUE_CHANNEL, LEDC_DUTY));
        ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, BLUE_CHANNEL));
        vTaskDelay(1000 / portTICK_PERIOD_MS);

        ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, BLUE_CHANNEL, 0));
        ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, BLUE_CHANNEL));
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }
}

/**
 * @brief Task to blink the green LED at a slow rate (1s ON, 1s OFF).
 *
 * This task sets the duty cycle for the green LED to create a slow blinking effect.
 *
 * @param arg Pointer to task parameters (not used).
 */
static void green_slow_task(void *arg)
{
    while (1)
    {
        ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, GREEN_CHANNEL, LEDC_DUTY));
        ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, GREEN_CHANNEL));
        vTaskDelay(1000 / portTICK_PERIOD_MS);

        ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, GREEN_CHANNEL, 0));
        ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, GREEN_CHANNEL));
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }
}

/**
 * @brief Task to blink the red LED at a slow rate (1s ON, 1s OFF).
 *
 * This task sets the duty cycle for the red LED to create a slow blinking effect.
 *
 * @param arg Pointer to task parameters (not used).
 */
static void red_slow_task(void *arg)
{
    while (1)
    {
        ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, RED_CHANNEL, LEDC_DUTY));
        ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, RED_CHANNEL));
        vTaskDelay(1000 / portTICK_PERIOD_MS);

        ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, RED_CHANNEL, 0));
        ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, RED_CHANNEL));
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }
}

/**
 * @brief Turns off the blue LED by setting its duty cycle to 0.
 *
 * This function sets the duty cycle of the blue LED channel to 0, effectively turning it off.
 */
static void blue_open(void)
{
    ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, BLUE_CHANNEL, 0));
    ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, BLUE_CHANNEL));
}

/**
 * @brief Turns off the green LED by setting its duty cycle to 0.
 *
 * This function sets the duty cycle of the green LED channel to 0, effectively turning it off.
 */
static void green_open(void)
{
    ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, GREEN_CHANNEL, 0));
    ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, GREEN_CHANNEL));
}

/**
 * @brief Turns off the red LED by setting its duty cycle to 0.
 *
 * This function sets the duty cycle of the red LED channel to 0, effectively turning it off.
 */
static void red_open(void)
{
    ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, RED_CHANNEL, 0));
    ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, RED_CHANNEL));
}

/**
 * @brief Turns off all LEDs by setting their duty cycles to 0.
 *
 * This function sets the duty cycle of the blue, green, and red LED channels to 0,
 * effectively turning off all the LEDs.
 */
static void close_all_leds(void)
{
    ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, BLUE_CHANNEL, LEDC_DUTY));
    ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, BLUE_CHANNEL));
    ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, GREEN_CHANNEL, LEDC_DUTY));
    ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, GREEN_CHANNEL));
    ESP_ERROR_CHECK(ledc_set_duty(LEDC_MODE, RED_CHANNEL, LEDC_DUTY));
    ESP_ERROR_CHECK(ledc_update_duty(LEDC_MODE, RED_CHANNEL));
}

/**
 * @brief Controls LED behavior based on the specified color and speed.
 *
 * This function stops any running LED tasks, turns off all LEDs, and then starts
 * a new task or operation based on the specified color and speed parameters.
 *
 * @param color The color of the LED to control:
 *              - 0 = Red
 *              - 1 = Green
 *              - 2 = Blue
 * @param speed The operation mode for the LED:
 *              - 0 = Slow blink
 *              - 1 = Fast blink
 *              - 2 = Constant on
 */
void ledtask_operation(int color, int speed)
{
    // Close all tasks
    if (BlueHandle != NULL)
    {
        vTaskDelete(BlueHandle);
        BlueHandle = NULL;
    }
    if (GreenHandle != NULL)
    {
        vTaskDelete(GreenHandle);
        GreenHandle = NULL;
    }
    if (RedHandle != NULL)
    {
        vTaskDelete(RedHandle);
        RedHandle = NULL;
    }

    // Turn off all lights
    close_all_leds();

    // Turn on the light again
    if (color == 0)
    {
        if (speed == 0)
            xTaskCreate(blue_slow_task, "LED Blink Task", 1024, NULL, 5, &BlueHandle);
        else if (speed == 1)
            xTaskCreate(blue_fast_task, "LED Blink Task", 1024, NULL, 5, &BlueHandle);
        else if (speed == 2)
            blue_open();
    }
    else if (color == 1)
    {
        if (speed == 0)
            xTaskCreate(green_slow_task, "LED Blink Task", 1024, NULL, 5, &GreenHandle);
        else if (speed == 1)
            xTaskCreate(green_fast_task, "LED Blink Task", 1024, NULL, 5, &GreenHandle);
        else if (speed == 2)
            green_open();
    }
    else
    {
        if (speed == 0)
            xTaskCreate(red_slow_task, "LED Blink Task", 1024, NULL, 5, &RedHandle);
        else if (speed == 1)
            xTaskCreate(red_fast_task, "LED Blink Task", 1024, NULL, 5, &RedHandle);
        else if (speed == 2)
            red_open();
    }
}

/**
 * @brief Initialize the network context with MQTT endpoint, certificates, and TLS settings.
 *
 * @return pdPASS on success, pdFAIL on failure.
 */
static BaseType_t prvInitializeNetworkContext(void)
{
    /* This is returned by this function. */
    BaseType_t xRet = pdPASS;

    /* This is used to store the error return of ESP-IDF functions. */
    esp_err_t xEspErrRet;

    /* Verify that the MQTT endpoint and thing name have been configured by the
     * user. */
    if (strlen(CONFIG_GRI_MQTT_ENDPOINT) == 0)
    {
        ESP_LOGE(TAG, "Empty endpoint for MQTT broker. Set endpoint by "
                      "running idf.py menuconfig, then Golden Reference Integration -> "
                      "Endpoint for MQTT Broker to use.");
        xRet = pdFAIL;
    }

    /* Initialize network context. */
    xNetworkContext.pcHostname = CONFIG_GRI_MQTT_ENDPOINT;
    xNetworkContext.xPort = CONFIG_GRI_MQTT_PORT;

    /* Get the device certificate from esp_secure_crt_mgr and put into network
     * context. */
    xEspErrRet = esp_secure_cert_get_device_cert(&xNetworkContext.pcClientCert,
                                                 &xNetworkContext.pcClientCertSize);

    if (xEspErrRet != ESP_OK)
    {
        ESP_LOGE(TAG, "Error in getting device certificate. Error: %s",
                 esp_err_to_name(xEspErrRet));

        xRet = pdFAIL;
    }

    /* Putting the Root CA certificate into the network context. */
    xNetworkContext.pcServerRootCA = root_cert_auth_start;
    xNetworkContext.pcServerRootCASize = root_cert_auth_end - root_cert_auth_start;

    if (xEspErrRet != ESP_OK)
    {
        ESP_LOGE(TAG, "Error in getting CA certificate. Error: %s",
                 esp_err_to_name(xEspErrRet));

        xRet = pdFAIL;
    }

#if CONFIG_ESP_SECURE_CERT_DS_PERIPHERAL

    /* If the digital signature peripheral is being used, get the digital
     * signature peripheral context from esp_secure_crt_mgr and put into
     * network context. */

    xNetworkContext.ds_data = esp_secure_cert_get_ds_ctx();

    if (xNetworkContext.ds_data == NULL)
    {
        ESP_LOGE(TAG, "Error in getting digital signature peripheral data.");
        xRet = pdFAIL;
    }
#else /* if CONFIG_ESP_SECURE_CERT_DS_PERIPHERAL */
    xEspErrRet = esp_secure_cert_get_priv_key(&xNetworkContext.pcClientKey,
                                              &xNetworkContext.pcClientKeySize);

    if (xEspErrRet != ESP_OK)
    {
        ESP_LOGE(TAG, "Error in getting private key. Error: %s",
                 esp_err_to_name(xEspErrRet));

        xRet = pdFAIL;
    }
#endif /* CONFIG_ESP_SECURE_CERT_DS_PERIPHERAL */

    xNetworkContext.pxTls = NULL;
    xNetworkContext.xTlsContextSemaphore = xSemaphoreCreateMutex();

    if (xNetworkContext.xTlsContextSemaphore == NULL)
    {
        ESP_LOGE(TAG, "Not enough memory to create TLS semaphore for global "
                      "network context.");

        xRet = pdFAIL;
    }

    return xRet;
}

/**
 * @brief Start the OTA update process after setting the code signing certificate.
 */
void startOTA(void)
{
    if (otaPal_SetCodeSigningCertificate(pcAwsCodeSigningCertPem))
    {
        vStartOTACodeSigningDemo();
    }
    else
    {
        ESP_LOGE(TAG,
                 "Failed to set the code signing certificate for the AWS OTA "
                 "library. OTA demo will not be started.");
    }
}

static void prvStartReporting(void)
{
    BaseType_t xResult;

    vStartReporting();

    ESP_LOGI(TAG, "Application version number: %u.%u.%u",
             CONFIG_GRI_OTA_DEMO_APP_VERSION_MAJOR,
             CONFIG_GRI_OTA_DEMO_APP_VERSION_MINOR,
             CONFIG_GRI_OTA_DEMO_APP_VERSION_BUILD);

    /* Initialize and start the coreMQTT-Agent network manager. This handles
     * establishing a TLS connection and MQTT connection to the MQTT broker.
     * This needs to be started before starting WiFi so it can handle WiFi
     * connection events. */
    xResult = xCoreMqttAgentManagerStart(&xNetworkContext);

    if (xResult != pdPASS)
    {
        ESP_LOGE(TAG, "Failed to initialize and start coreMQTT-Agent network "
                      "manager.");

        configASSERT(xResult == pdPASS);
    }
}

static void cert_init(void)
{
    /* This is used to store the return of initialization functions. */
    BaseType_t xRet;

    /* This is used to store the error return of ESP-IDF functions. */
    esp_err_t xEspErrRet;

    /* Initialize global network context. */
    xRet = prvInitializeNetworkContext();

    if (xRet != pdPASS)
    {
        ESP_LOGE(TAG, "Failed to initialize global network context.");
        return;
    }

    // Initialize network interface only if WiFi is configured
    if(is_wifi_configured() || is_eth_configured())
    {
        ESP_ERROR_CHECK(esp_netif_init());
        
        /* Initialize ESP-Event library default event loop.
         * This handles WiFi and TCP/IP events and this needs to be called before
         * starting WiFi and the coreMQTT-Agent network manager. */
        ESP_ERROR_CHECK(esp_event_loop_create_default());
    }
    
    // Start PPP client only if 4G is configured and enabled
    if(is_4g_configured())
    {
        start_ppp();
    }

    prvStartReporting();
}

/**
 * @brief Erase the entire NVS partition to clear saved SSID and password.
 */
void erase_nvs_entries(void)
{
    esp_err_t err;

    // Erase the NVS partition
    err = nvs_flash_erase();
    if (err == ESP_OK)
    {
        ESP_LOGI("NVS", "NVS partition erased successfully");
    }
    else
    {
        ESP_LOGE("NVS", "Error (%s) erasing NVS partition", esp_err_to_name(err));
    }
}

/**
 * @brief FreeRTOS task to handle button presses and perform actions based on press duration.
 */
void button_task(void *arg)
{
    uint32_t press_start_time = 0;
    uint32_t press_duration = 0;
    bool button_pressed = false;

    while (1)
    {
        if (gpio_get_level(BUTTON_GPIO) == 0)
        { // Button pressed
            if (!button_pressed)
            {
                button_pressed = true;
                press_start_time = esp_timer_get_time(); // Record pressing time
            }
        }
        else
        { // Button released
            if (button_pressed)
            {
                button_pressed = false;
                press_duration = esp_timer_get_time() - press_start_time; // Calculate the press time
                if (press_duration >= LONG_PRESS_TIME_MS * 1000)
                {
                    ledtask_operation(GREEN, FAST);
                    ESP_LOGI(TAG, "Button pressed for 10 seconds, erasing flash...");
                    // Erasing a specific entry in NVS
                    erase_nvs_entries();
                    // Restart device
                    esp_restart();
                }
            }
        }
        vTaskDelay(500 / portTICK_PERIOD_MS); // Check the button state every 500 milliseconds
    }
}

static void button_init(void)
{
    // Configure the button pin as input mode
    gpio_config_t io_conf;
    io_conf.intr_type = GPIO_INTR_DISABLE; // No interrupts
    io_conf.mode = GPIO_MODE_INPUT;
    io_conf.pin_bit_mask = (1ULL << BUTTON_GPIO);
    io_conf.pull_down_en = GPIO_PULLDOWN_DISABLE;
    io_conf.pull_up_en = GPIO_PULLUP_ENABLE;
    gpio_config(&io_conf);

    // Creating a button task
    xTaskCreate(button_task, "button_task", 5120, NULL, 10, NULL);
}

static esp_err_t init_spiffs(void)
{
    esp_vfs_spiffs_conf_t rcp_fw_conf = {.base_path = "/" CONFIG_RCP_PARTITION_NAME,
                                         .partition_label = CONFIG_RCP_PARTITION_NAME,
                                         .max_files = 10,
                                         .format_if_mount_failed = false};
    ESP_RETURN_ON_ERROR(esp_vfs_spiffs_register(&rcp_fw_conf), TAG, "Failed to mount rcp firmware storage");
    return ESP_OK;
}

static void ot_br_init(void)
{
    // Used eventfds:
    // * netif
    // * task queue
    // * border router
    // * spi interface
    esp_vfs_eventfd_config_t eventfd_config = {
        .max_fds = 4,
    };

    esp_openthread_platform_config_t platform_config = {
        .radio_config = ESP_OPENTHREAD_DEFAULT_RADIO_CONFIG(),
        .host_config = ESP_OPENTHREAD_DEFAULT_HOST_CONFIG(),
        .port_config = ESP_OPENTHREAD_DEFAULT_PORT_CONFIG(),
    };
    esp_rcp_update_config_t rcp_update_config = ESP_OPENTHREAD_RCP_UPDATE_CONFIG();
    ESP_ERROR_CHECK(esp_vfs_eventfd_register(&eventfd_config));

    ESP_ERROR_CHECK(init_spiffs());

    // LED slow flashing green 
    ledtask_operation(GREEN,SLOW);

    ESP_ERROR_CHECK(mdns_init());
    ESP_ERROR_CHECK(mdns_hostname_set("esp-ot-br"));

    launch_openthread_border_router(&platform_config, &rcp_update_config); 
}