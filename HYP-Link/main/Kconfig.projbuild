menu "SNTP Configuration"
    config SNTP_TIME_SERVER
        string "SNTP server name"
        default "pool.ntp.org"
        help
            Hostname of the main SNTP server.  
endmenu

menu "Example Configuration"

    config MQTT_BROKER_ENDPOINT
        string "Endpoint of the MQTT broker to connect to"
        default "test.mosquitto.org"
        help
            This example can be run with any MQTT broker, that supports server authentication.

    config MQTT_BROKER_PORT
        int "Port of the MQTT broker use"
        default 8883
        help
            In general, port 8883 is for secured MQTT connections.
            Port 443 requires use of the ALPN TLS extension with the ALPN protocol name.
            When using port 8883, ALPN is not required.

    config MQTT_NETWORK_BUFFER_SIZE
        int "Size of the network buffer for MQTT packets"
        range 1024 2048
        default 1024
        help
            Size of the network buffer for MQTT packets.

    config PROVISIONING_TEMPLATE_NAME
        string "Name of the provisioning template"
        default "FleetProvisioningDemoTemplate"
        help
            Name of the provisioning template to use for the RegisterThing
            portion of the Fleet Provisioning workflow.

    choice EXAMPLE_CHOOSE_PKI_ACCESS_METHOD
        prompt "Choose PKI credentials access method"
        default EXAMPLE_USE_PLAIN_FLASH_STORAGE
        help
            ESP devices support multiple ways to secure store the PKI credentials.
            Currently Secure Element (ATECC608A) and DS peripheral are supported.
            The default behaviour is to access the PKI credentials which are embedded in the binary.
            Consult the ESP-TLS documentation in ESP-IDF Programming guide for more details.

        config EXAMPLE_USE_PLAIN_FLASH_STORAGE
        bool "Use flash storage (default)"
        help
            This option expects the Private key and Device certificate to be embedded in the binary.
            This is the default behaviour.
    endchoice

endmenu

menu "CoAP Server Configuration"

    config EXAMPLE_COAP_PSK_KEY
        string "Preshared Key (PSK) to used in the connection from the CoAP client"
        depends on COAP_MBEDTLS_PSK
        default "secret-key"
        help
            The Preshared Key to use to encrypt the communicatons. The same key must be
            used at both ends of the CoAP connection, and the CoaP client must request
            an URI prefixed with coaps:// instead of coap:// for DTLS to be used.

    config EXAMPLE_COAP_LISTEN_PORT
        string "CoAP Listen port"
        default "5683"
        help
            Port number to listen for CoAP traffic.

    config EXAMPLE_COAPS_LISTEN_PORT
        string "CoAP Secure Listen port"
        default "5684"
        depends on COAP_MBEDTLS_PSK || COAP_MBEDTLS_PKI
        help
            Port number to listen for CoAP secure ((D)TLS) traffic.

    config EXAMPLE_COAP_WEBSOCKET_PORT
        string "CoAP Websocket port"
        default "80"
        depends on COAP_WEBSOCKETS
        help
            Port number to listen for WebSocket traffic on.

            The default is 80.

    config EXAMPLE_COAP_WEBSOCKET_SECURE_PORT
        string "CoAP Websocket Secure port"
        default "443"
        depends on COAP_WEBSOCKETS && (COAP_MBEDTLS_PSK || COAP_MBEDTLS_PKI)
        help
            Port number to listen for WebSocket Secure (TLS) traffic on.

            The default is 443.

    config COAP_FAST_PATH_ENABLED
        bool "Enable CoAP fast path optimization"
        default y
        help
            Enable fast path processing for CoAP requests to reduce callback latency.
            This moves heavy processing to background tasks and uses PSRAM for buffers.

    config COAP_RESPONSE_QUEUE_SIZE
        int "CoAP response queue size"
        default 8
        range 2 32
        depends on COAP_FAST_PATH_ENABLED
        help
            Size of the queue for fast CoAP responses.

    config COAP_PROCESSING_QUEUE_SIZE
        int "CoAP processing queue size"
        default 4
        range 2 16
        depends on COAP_FAST_PATH_ENABLED
        help
            Size of the queue for background CoAP processing.

    config COAP_MAX_SESSIONS
        int "Maximum CoAP sessions"
        default 20
        range 10 40
        help
            Maximum number of concurrent CoAP sessions.

    config COAP_MAX_MESSAGE_SIZE
        int "Maximum CoAP message size"
        default 2048
        range 1024 4096
        help
            Maximum size for CoAP messages.

endmenu



menu "Featured FreeRTOS IoT Integration"
    config APP_WIFI_PROV_SHOW_QR
        bool "Show provisioning QR code"
        default y
        help
            Show the QR code for provisioning.

    choice APP_WIFI_PROV_TRANSPORT
        bool "Provisioning Transport method"
        default APP_WIFI_PROV_TRANSPORT_BLE
        help
            Wi-Fi provisioning component offers both, SoftAP and BLE transports. Choose any one.
    config APP_WIFI_PROV_TRANSPORT_SOFTAP
        bool "Soft AP"
    config APP_WIFI_PROV_TRANSPORT_BLE
        bool "BLE"
        select BT_ENABLED
    endchoice

    config APP_WIFI_PROV_TRANSPORT
        int
        default 1 if APP_WIFI_PROV_TRANSPORT_SOFTAP
        default 2 if APP_WIFI_PROV_TRANSPORT_BLE

        menu "OTA demo configurations"
            config GRI_OTA_DEMO_MAX_FILE_PATH_SIZE
                int "Max file path size."
                default 260
                help
                    The maximum size of the file paths used in the demo.

            config GRI_OTA_DEMO_MAX_STREAM_NAME_SIZE
                int "Max stream name size."
                default 128
                help
                    The maximum size of the stream name required for downloading update file from streaming service.

            config GRI_OTA_DEMO_TASK_DELAY_MS
                int "OTA statistic output delay milliseconds."
                default 1000
                help
                    The delay used in the OTA demo task to periodically output the OTA statistics like number of packets received, dropped, processed and queued per connection.

            config GRI_OTA_DEMO_MQTT_TIMEOUT_MS
                int "MQTT operation timeout milliseconds."
                default 5000
                help
                    The maximum time for which OTA demo waits for an MQTT operation to be complete. This involves receiving an acknowledgment for broker for SUBSCRIBE, UNSUBSCRIBE and non QOS0 publishes.

            config GRI_OTA_DEMO_AGENT_TASK_PRIORITY
                int "OTA agent task priority."
                default 4

            config GRI_OTA_DEMO_AGENT_TASK_STACK_SIZE
                int "OTA agent task stack size."
                default 4096

            config GRI_OTA_DEMO_DEMO_TASK_PRIORITY
                int "OTA demo task priority."
                default 1

            config GRI_OTA_DEMO_DEMO_TASK_STACK_SIZE
                int "OTA demo task stack size."
                default 3072

            config GRI_OTA_MAX_NUM_DATA_BUFFERS
                int "OTA buffer number."
                default 2

        endmenu # OTA demo configurations

    config GRI_MQTT_ENDPOINT
        string "Endpoint for MQTT Broker to use"
        default ""

    config GRI_MQTT_PORT
        int "Port for MQTT Broker to use"
        default 8883

    config GRI_THING_NAME
        string "Thing name"
        default "ThingName"

    menu "coreMQTT-Agent Manager Configurations"

        config GRI_MQTT_AGENT_TASK_STACK_SIZE
            int "coreMQTT-Agent task stack size"
            default 4096

        config GRI_MQTT_AGENT_TASK_PRIORITY
            int "coreMQTT-Agent task priority"
            default 4

        config GRI_CONNECTION_TASK_STACK_SIZE
            int "Connection handling task stack size"
            default 3072

        config GRI_CONNECTION_TASK_PRIORITY
            int "Connection handling task priority"
            default 4

        config GRI_RETRY_MAX_BACKOFF_DELAY_MS
            int "Maximum backoff delay on connection retry in milliseconds"
            default 5000

        config GRI_RETRY_BACKOFF_BASE_MS
            int "Base back-off delay on connection retry in milliseconds"
            default 500

        config GRI_MQTT_AGENT_NETWORK_BUFFER_SIZE
            int "coreMQTT-Agent network buffer size"
            default 10000

        config GRI_MQTT_AGENT_COMMAND_QUEUE_LENGTH
            int "coreMQTT-Agent command queue length"
            default 10

        config GRI_MQTT_AGENT_KEEP_ALIVE_INTERVAL_SECONDS
            int "coreMQTT-Agent keep alive interval in seconds"
            default 60
            help
                The maximum time interval in seconds which is allowed to elapsed between two Control Packets.

        config GRI_MQTT_AGENT_CONNACK_RECV_TIMEOUT_MS
            int "Timeout for receiving CONNACK in milliseconds"
            default 1000


    endmenu # coreMQTT-Agent Manager Configurations

    config GRI_ENABLE_TEMPERATURE_PUB_SUB_AND_LED_CONTROL_DEMO
        bool "Enable temperature sensor pub sub and LED control demo"
        depends on !GRI_RUN_QUALIFICATION_TEST
        default y

    menu "Temperature pub sub and LED control demo configurations"
        depends on GRI_ENABLE_TEMPERATURE_PUB_SUB_AND_LED_CONTROL_DEMO

        config GRI_TEMPERATURE_PUB_SUB_AND_LED_CONTROL_DEMO_STRING_BUFFER_LENGTH
            int "Topic name and payload buffer length"
            default 150
            help
                Size of statically allocated buffers for holding topic names and payloads.

        config GRI_TEMPERATURE_PUB_SUB_AND_LED_CONTROL_DEMO_DELAY_BETWEEN_PUBLISH_OPERATIONS_MS
            int "Delay between publishes in milliseconds"
            default 3000
            help
                Delay for the synchronous publisher task between publishes.

        config GRI_TEMPERATURE_PUB_SUB_AND_LED_CONTROL_DEMO_MAX_COMMAND_SEND_BLOCK_TIME_MS
            int "coreMQTT-Agent command post block time in milliseconds."
            default 500
            help
                The maximum amount of time in milliseconds to wait for the commands to be posted to the MQTT agent should the MQTT agent's command queue be full. Tasks wait in the Blocked state, so don't use any CPU time.

        config GRI_TEMPERATURE_PUB_SUB_AND_LED_CONTROL_DEMO_QOS_LEVEL
            int "QoS level of MQTT operations"
            default 1
            range 0 2
            help
                The QoS level of MQTT messages sent by this demo. This must be 0 or 1 if using AWS as AWS only supports levels 0 or 1. If using another MQTT broker, that supports QoS level 2, this can be set to 2.

        config GRI_TEMPERATURE_PUB_SUB_AND_LED_CONTROL_DEMO_TASK_PRIORITY
            int "Temperature sensor publishing task priority."
            default 1
            help
                The task priority of the temperature sensor publishing task.

        config GRI_TEMPERATURE_PUB_SUB_AND_LED_CONTROL_DEMO_TASK_STACK_SIZE
            int "Temperature sensor publishing task stack size."
            default 3072
            help
                The task stack size of the temperature sensor publishing task.

    endmenu # Temperature pub sub and LED control demo configurations

    config GRI_ENABLE_OTA_DEMO
        bool "Enable OTA demo"
        depends on !GRI_RUN_QUALIFICATION_TEST
        default y

    menu "OTA demo configurations"
        depends on GRI_ENABLE_OTA_DEMO

        config GRI_OTA_DEMO_MAX_FILE_PATH_SIZE
            int "Max file path size."
            default 260
            help
                The maximum size of the file paths used in the demo.

        config GRI_OTA_DEMO_MAX_STREAM_NAME_SIZE
            int "Max stream name size."
            default 128
            help
                The maximum size of the stream name required for downloading update file from streaming service.

        config GRI_OTA_DEMO_TASK_DELAY_MS
            int "OTA statistic output delay milliseconds."
            default 1000
            help
                The delay used in the OTA demo task to periodically output the OTA statistics like number of packets received, dropped, processed and queued per connection.

        config GRI_OTA_DEMO_MQTT_TIMEOUT_MS
            int "MQTT operation timeout milliseconds."
            default 5000
            help
                The maximum time for which OTA demo waits for an MQTT operation to be complete. This involves receiving an acknowledgment for broker for SUBSCRIBE, UNSUBSCRIBE and non QOS0 publishes.

        config GRI_OTA_DEMO_AGENT_TASK_PRIORITY
            int "OTA agent task priority."
            default 4

        config GRI_OTA_DEMO_AGENT_TASK_STACK_SIZE
            int "OTA agent task stack size."
            default 4096

        config GRI_OTA_DEMO_DEMO_TASK_PRIORITY
            int "OTA demo task priority."
            default 1

        config GRI_OTA_DEMO_DEMO_TASK_STACK_SIZE
            int "OTA demo task stack size."
            default 3072

        config GRI_OTA_DEMO_APP_VERSION_MAJOR
            int "Application version major."
            default 0

        config GRI_OTA_DEMO_APP_VERSION_MINOR
            int "Application version minor."
            default 0

        config GRI_OTA_DEMO_APP_VERSION_BUILD
            int "Application version build."
            default 0

        config GRI_OTA_MAX_NUM_DATA_BUFFERS
            int "OTA buffer number."
            default 2

    endmenu # OTA demo configurations

endmenu # Golden Reference Integration


menu "PPP Configuration"
    config EXAMPLE_MODEM_PPP_APN
        string "Set MODEM APN"
        default "internet"
        help
            Set APN (Access Point Name), a logical name to choose data network

    menu "UART Configuration"
        config EXAMPLE_MODEM_UART_TX_PIN
            int "TXD Pin Number"
            default 16
            range 0 56
            help
                Pin number of UART TX.

        config EXAMPLE_MODEM_UART_RX_PIN
            int "RXD Pin Number"
            default 15
            range 0 56
            help
                Pin number of UART RX.

        config EXAMPLE_MODEM_UART_RTS_PIN
            int "RTS Pin Number"
            default 48
            range 0 56
            help
                Pin number of UART RTS.

        config EXAMPLE_MODEM_UART_CTS_PIN
            int "CTS Pin Number"
            default 14
            range 0 56
            help
                Pin number of UART CTS.

        config EXAMPLE_MODEM_UART_EVENT_TASK_STACK_SIZE
            int "UART Event Task Stack Size"
            range 2000 6000
            default 2048
            help
                Stack size of UART event task.

        config EXAMPLE_MODEM_UART_EVENT_TASK_PRIORITY
            int "UART Event Task Priority"
            range 3 22
            default 5
            help
                Priority of UART event task.

        config EXAMPLE_MODEM_UART_EVENT_QUEUE_SIZE
            int "UART Event Queue Size"
            range 10 40
            default 30
            help
                Length of UART event queue.

        config EXAMPLE_MODEM_UART_PATTERN_QUEUE_SIZE
            int "UART Pattern Queue Size"
            range 10 40
            default 20
            help
                Length of UART pattern queue.

        config EXAMPLE_MODEM_UART_TX_BUFFER_SIZE
            int "UART TX Buffer Size"
            range 256 2048
            default 512
            help
                Buffer size of UART TX buffer.

        config EXAMPLE_MODEM_UART_RX_BUFFER_SIZE
            int "UART RX Buffer Size"
            range 256 2048
            default 1024
            help
                Buffer size of UART RX buffer.
    endmenu
endmenu
menu "Current Sensor OTA Configuration"
    config FIRMWARE_VERSION
        string "Firmware version"
        default "0.0.1"
        help
            Set the firmware version of the current sensor
endmenu